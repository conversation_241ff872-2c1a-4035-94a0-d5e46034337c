{"base_model": "microsoft/DialoGPT-medium", "dataset": "Abhaykoul/Dhanishtha-2.0-SUPERTHINKER", "output_dir": "./helpingai-finetuned", "convert_from_existing": true, "max_seq_length": 8192, "hidden_size": 1024, "num_layers": 24, "num_heads": 16, "num_kv_heads": 8, "use_sliding_window": true, "sliding_window": 4096, "rope_scaling": {"type": "linear", "factor": 2.0}, "attention_dropout": 0.1, "hidden_dropout": 0.1, "use_lora": true, "lora_r": 32, "lora_alpha": 32, "lora_dropout": 0.05, "lora_targets": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"], "use_rslora": false, "num_epochs": 3, "batch_size": 2, "eval_batch_size": 2, "gradient_accumulation_steps": 8, "learning_rate": 0.0002, "weight_decay": 0.01, "warmup_steps": 100, "eval_split": 0.1, "logging_steps": 10, "save_steps": 500, "eval_steps": 500, "optimizer": "adamw_torch", "lr_scheduler": "cosine", "fp16": true, "gradient_checkpointing": true, "report_to": "none"}