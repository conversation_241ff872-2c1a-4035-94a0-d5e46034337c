{"base_model": "microsoft/DialoGPT-large", "dataset": "Abhaykoul/Dhanishtha-2.0-SUPERTHINKER", "output_dir": "./helpingai-large-64k-finetuned", "convert_from_existing": true, "max_seq_length": 65536, "hidden_size": 2560, "num_layers": 32, "num_heads": 32, "num_kv_heads": 8, "use_sliding_window": true, "sliding_window": 4096, "rope_scaling": {"type": "linear", "factor": 16.0}, "attention_dropout": 0.1, "hidden_dropout": 0.1, "use_lora": true, "lora_r": 64, "lora_alpha": 64, "lora_dropout": 0.05, "lora_targets": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"], "use_rslora": true, "num_epochs": 2, "batch_size": 1, "eval_batch_size": 1, "gradient_accumulation_steps": 16, "learning_rate": 0.0001, "weight_decay": 0.01, "warmup_steps": 200, "eval_split": 0.05, "logging_steps": 20, "save_steps": 1000, "eval_steps": 1000, "optimizer": "adamw_8bit", "lr_scheduler": "cosine", "fp16": true, "gradient_checkpointing": true, "report_to": "wandb"}