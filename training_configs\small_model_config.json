{"base_model": "microsoft/DialoGPT-small", "dataset": "Abhaykoul/Dhanishtha-2.0-SUPERTHINKER", "output_dir": "./helpingai-small-finetuned", "convert_from_existing": true, "max_seq_length": 4096, "hidden_size": 768, "num_layers": 12, "num_heads": 12, "num_kv_heads": 4, "use_sliding_window": false, "sliding_window": 2048, "rope_scaling": {"type": "linear", "factor": 1.5}, "attention_dropout": 0.1, "hidden_dropout": 0.1, "use_lora": true, "lora_r": 16, "lora_alpha": 16, "lora_dropout": 0.05, "lora_targets": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"], "use_rslora": false, "num_epochs": 5, "batch_size": 4, "eval_batch_size": 4, "gradient_accumulation_steps": 4, "learning_rate": 0.0003, "weight_decay": 0.01, "warmup_steps": 50, "eval_split": 0.1, "logging_steps": 5, "save_steps": 250, "eval_steps": 250, "optimizer": "adamw_torch", "lr_scheduler": "cosine", "fp16": true, "gradient_checkpointing": true, "report_to": "none"}