#!/usr/bin/env python3
"""
Training script to fine-tune any model to use the enhanced HelpingAI architecture.
This script uses pure transformers without unsloth dependencies.

Recommended training data sizes:
- Small models (1-3B): 10K-50K high-quality samples
- Medium models (7-8B): 50K-200K samples  
- Large models (13B+): 100K-500K samples

For reasoning/thinking models: Add 20-30% more data focused on step-by-step reasoning.
"""

import os
import sys
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, AutoConfig,
    TrainingArguments, Trainer, DataCollatorForLanguageModeling,
    get_linear_schedule_with_warmup
)
from datasets import load_dataset, Dataset
import pandas as pd
from peft import LoraConfig, get_peft_model, TaskType, PeftModel
import json
import logging
from typing import Dict, List, Optional, Union
import argparse
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add HelpingAI to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'HelpingAI_'))

try:
    from configuration_HelpingAI import HelpingAIConfig
    from modeling_HelpingAI import HelpingAIForCausalLM
except ImportError as e:
    logger.error(f"Failed to import HelpingAI modules: {e}")
    logger.info("Make sure the HelpingAI_ directory is in the same folder as this script")
    sys.exit(1)


class ModelConverter:
    """Converts existing models to HelpingAI architecture."""
    
    def __init__(self, source_model_name: str, target_config: HelpingAIConfig):
        self.source_model_name = source_model_name
        self.target_config = target_config
        
    def convert_model(self) -> HelpingAIForCausalLM:
        """Convert a source model to HelpingAI architecture."""
        logger.info(f"Converting {self.source_model_name} to HelpingAI architecture...")
        
        # Load source model for weight initialization
        try:
            source_model = AutoModelForCausalLM.from_pretrained(
                self.source_model_name,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True
            )
            source_config = source_model.config
            
            # Update target config with compatible dimensions
            self.target_config.vocab_size = source_config.vocab_size
            if hasattr(source_config, 'hidden_size'):
                self.target_config.hidden_size = source_config.hidden_size
            if hasattr(source_config, 'num_hidden_layers'):
                self.target_config.num_hidden_layers = min(source_config.num_hidden_layers, 32)
            if hasattr(source_config, 'num_attention_heads'):
                self.target_config.num_attention_heads = source_config.num_attention_heads
                
        except Exception as e:
            logger.warning(f"Could not load source model for dimension matching: {e}")
            logger.info("Using default HelpingAI configuration")
        
        # Create new HelpingAI model
        target_model = HelpingAIForCausalLM(self.target_config)
        
        # Try to transfer compatible weights
        if 'source_model' in locals():
            self._transfer_weights(source_model, target_model)
        
        logger.info("Model conversion completed!")
        return target_model
    
    def _transfer_weights(self, source_model, target_model):
        """Transfer compatible weights from source to target model."""
        logger.info("Transferring compatible weights...")
        
        source_state = source_model.state_dict()
        target_state = target_model.state_dict()
        
        transferred = 0
        for name, param in target_state.items():
            if name in source_state and source_state[name].shape == param.shape:
                param.data.copy_(source_state[name].data)
                transferred += 1
        
        logger.info(f"Transferred {transferred}/{len(target_state)} parameters")


class HelpingAITrainer:
    """Enhanced trainer for HelpingAI models."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.setup_model_and_tokenizer()
        self.setup_lora()
        
    def setup_model_and_tokenizer(self):
        """Setup model and tokenizer."""
        logger.info("Setting up model and tokenizer...")
        
        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.config['base_model'],
            trust_remote_code=True,
            use_fast=True
        )
        
        # Add special tokens if needed
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
            
        # Setup HelpingAI configuration
        helpingai_config = HelpingAIConfig(
            vocab_size=len(self.tokenizer),
            max_position_embeddings=self.config.get('max_seq_length', 65536),
            hidden_size=self.config.get('hidden_size', 2560),
            num_hidden_layers=self.config.get('num_layers', 32),
            num_attention_heads=self.config.get('num_heads', 32),
            num_key_value_heads=self.config.get('num_kv_heads', 8),  # GQA for efficiency
            use_sliding_window=self.config.get('use_sliding_window', True),
            sliding_window=self.config.get('sliding_window', 4096),
            rope_scaling=self.config.get('rope_scaling', {
                "type": "linear",
                "factor": 4.0
            }),
            attention_dropout=self.config.get('attention_dropout', 0.1),
            hidden_dropout=self.config.get('hidden_dropout', 0.1),
        )
        
        # Convert or create model
        if self.config.get('convert_from_existing', True):
            converter = ModelConverter(self.config['base_model'], helpingai_config)
            self.model = converter.convert_model()
        else:
            self.model = HelpingAIForCausalLM(helpingai_config)
            
        # Resize token embeddings if needed
        self.model.resize_token_embeddings(len(self.tokenizer))
        
        logger.info(f"Model setup complete. Parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        
    def setup_lora(self):
        """Setup LoRA for efficient fine-tuning."""
        if not self.config.get('use_lora', True):
            return
            
        logger.info("Setting up LoRA...")
        
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=self.config.get('lora_r', 32),
            lora_alpha=self.config.get('lora_alpha', 32),
            lora_dropout=self.config.get('lora_dropout', 0.05),
            target_modules=self.config.get('lora_targets', [
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ]),
            bias="none",
            use_rslora=self.config.get('use_rslora', False),
        )
        
        self.model = get_peft_model(self.model, lora_config)
        self.model.print_trainable_parameters()
        
    def prepare_dataset(self):
        """Prepare training dataset."""
        logger.info("Preparing dataset...")
        
        # Load dataset
        if self.config['dataset'].startswith('http') or '/' in self.config['dataset']:
            dataset = load_dataset(self.config['dataset'], split='train')
        else:
            # Local file
            if self.config['dataset'].endswith('.json'):
                with open(self.config['dataset'], 'r') as f:
                    data = json.load(f)
                dataset = Dataset.from_list(data)
            else:
                dataset = load_dataset('text', data_files=self.config['dataset'], split='train')
        
        # Convert to conversation format
        def format_conversations(examples):
            conversations = []
            
            if 'conversations' in examples:
                # Already in conversation format
                conversations = examples['conversations']
            elif 'instruction' in examples and 'output' in examples:
                # Instruction-output format
                for inst, out in zip(examples['instruction'], examples['output']):
                    conversations.append([
                        {"role": "user", "content": inst},
                        {"role": "assistant", "content": out}
                    ])
            elif 'text' in examples:
                # Raw text format - split by conversation markers
                for text in examples['text']:
                    if '<|im_start|>' in text:
                        # ChatML format
                        conversations.append(self._parse_chatml(text))
                    else:
                        # Treat as single assistant response
                        conversations.append([
                            {"role": "user", "content": "Continue the conversation."},
                            {"role": "assistant", "content": text}
                        ])
            
            # Apply chat template
            formatted_texts = []
            for conv in conversations:
                try:
                    formatted_text = self.tokenizer.apply_chat_template(
                        conv,
                        tokenize=False,
                        add_generation_prompt=False
                    )
                    formatted_texts.append(formatted_text)
                except Exception as e:
                    logger.warning(f"Failed to format conversation: {e}")
                    continue
                    
            return {"text": formatted_texts}
        
        # Process dataset
        dataset = dataset.map(
            format_conversations,
            batched=True,
            remove_columns=dataset.column_names
        )
        
        # Tokenize
        def tokenize_function(examples):
            return self.tokenizer(
                examples["text"],
                truncation=True,
                padding=False,
                max_length=self.config.get('max_seq_length', 4096),
                return_overflowing_tokens=False,
            )
        
        dataset = dataset.map(
            tokenize_function,
            batched=True,
            remove_columns=dataset.column_names
        )
        
        # Shuffle and split
        dataset = dataset.shuffle(seed=42)
        
        if self.config.get('eval_split', 0.1) > 0:
            split_dataset = dataset.train_test_split(
                test_size=self.config['eval_split'],
                seed=42
            )
            self.train_dataset = split_dataset['train']
            self.eval_dataset = split_dataset['test']
        else:
            self.train_dataset = dataset
            self.eval_dataset = None
            
        logger.info(f"Training samples: {len(self.train_dataset):,}")
        if self.eval_dataset:
            logger.info(f"Evaluation samples: {len(self.eval_dataset):,}")
    
    def _parse_chatml(self, text: str) -> List[Dict[str, str]]:
        """Parse ChatML format text into conversation."""
        conversation = []
        parts = text.split('<|im_start|>')
        
        for part in parts[1:]:  # Skip first empty part
            if '<|im_end|>' not in part:
                continue
                
            content = part.split('<|im_end|>')[0].strip()
            if content.startswith('user\n'):
                role = 'user'
                message = content[5:].strip()
            elif content.startswith('assistant\n'):
                role = 'assistant'
                message = content[10:].strip()
            else:
                continue
                
            conversation.append({"role": role, "content": message})
        
        return conversation
    
    def train(self):
        """Train the model."""
        logger.info("Starting training...")
        
        # Prepare dataset
        self.prepare_dataset()
        
        # Training arguments
        training_args = TrainingArguments(
            output_dir=self.config['output_dir'],
            num_train_epochs=self.config.get('num_epochs', 3),
            per_device_train_batch_size=self.config.get('batch_size', 2),
            per_device_eval_batch_size=self.config.get('eval_batch_size', 2),
            gradient_accumulation_steps=self.config.get('gradient_accumulation_steps', 4),
            learning_rate=self.config.get('learning_rate', 2e-4),
            weight_decay=self.config.get('weight_decay', 0.01),
            warmup_steps=self.config.get('warmup_steps', 100),
            logging_steps=self.config.get('logging_steps', 10),
            save_steps=self.config.get('save_steps', 500),
            eval_steps=self.config.get('eval_steps', 500) if self.eval_dataset else None,
            evaluation_strategy="steps" if self.eval_dataset else "no",
            save_total_limit=3,
            load_best_model_at_end=True if self.eval_dataset else False,
            metric_for_best_model="eval_loss" if self.eval_dataset else None,
            greater_is_better=False,
            report_to=self.config.get('report_to', 'none'),
            run_name=f"helpingai-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
            optim=self.config.get('optimizer', 'adamw_torch'),
            lr_scheduler_type=self.config.get('lr_scheduler', 'cosine'),
            fp16=self.config.get('fp16', True),
            dataloader_pin_memory=False,
            gradient_checkpointing=self.config.get('gradient_checkpointing', True),
            remove_unused_columns=False,
        )
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
            pad_to_multiple_of=8,
        )
        
        # Trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=self.train_dataset,
            eval_dataset=self.eval_dataset,
            data_collator=data_collator,
            tokenizer=self.tokenizer,
        )
        
        # Memory stats
        if torch.cuda.is_available():
            gpu_stats = torch.cuda.get_device_properties(0)
            start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)
            max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)
            logger.info(f"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.")
            logger.info(f"{start_gpu_memory} GB of memory reserved.")
        
        # Train
        trainer_stats = trainer.train()
        
        # Final memory stats
        if torch.cuda.is_available():
            used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)
            used_memory_for_training = round(used_memory - start_gpu_memory, 3)
            used_percentage = round(used_memory / max_memory * 100, 3)
            training_percentage = round(used_memory_for_training / max_memory * 100, 3)
            
            logger.info(f"Training completed in {trainer_stats.metrics['train_runtime']:.2f} seconds")
            logger.info(f"Peak reserved memory = {used_memory} GB ({used_percentage}%)")
            logger.info(f"Peak reserved memory for training = {used_memory_for_training} GB ({training_percentage}%)")
        
        # Save model
        trainer.save_model()
        self.tokenizer.save_pretrained(self.config['output_dir'])
        
        logger.info(f"Model saved to {self.config['output_dir']}")
        
        return trainer_stats


def main():
    parser = argparse.ArgumentParser(description="Train HelpingAI model")
    parser.add_argument("--config", type=str, required=True, help="Path to training config JSON")
    args = parser.parse_args()
    
    # Load config
    with open(args.config, 'r') as f:
        config = json.load(f)
    
    # Train
    trainer = HelpingAITrainer(config)
    trainer.train()


if __name__ == "__main__":
    main()
