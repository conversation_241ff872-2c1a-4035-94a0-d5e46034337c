{"add_bos_token": false, "add_eos_token": false, "add_prefix_space": false, "added_tokens_decoder": {"0": {"content": "<|endoftext|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "1": {"content": "<|padding|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "50254": {"content": "                        ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50255": {"content": "                       ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50256": {"content": "                      ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50257": {"content": "                     ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50258": {"content": "                    ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50259": {"content": "                   ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50260": {"content": "                  ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50261": {"content": "                 ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50262": {"content": "                ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50263": {"content": "               ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50264": {"content": "              ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50265": {"content": "             ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50266": {"content": "            ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50267": {"content": "           ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50268": {"content": "          ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50269": {"content": "         ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50270": {"content": "        ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50271": {"content": "       ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50272": {"content": "      ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50273": {"content": "     ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50274": {"content": "    ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50275": {"content": "   ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50276": {"content": "  ", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50277": {"content": "<|pad|>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50278": {"content": "<|im_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "50279": {"content": "<|im_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "50280": {"content": "[PAD]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "additional_special_tokens": ["<|im_start|>", "<|im_end|>"], "bos_token": "<|im_start|>", "chat_template": [{"name": "default", "template": "{% for message in messages %}{{'<|im_start|>' + message['role'] + '\\n' + message['content'] + '<|im_end|>' + '\\n'}}{% endfor %}{% if add_generation_prompt %}{{ '<|im_start|>assistant\\n' }}{% endif %}"}, {"name": "tool_use", "template": "\\n{%- macro json_to_python_type(json_spec) %}\\n{%- set basic_type_map = {\\n    \"string\": \"str\",\\n    \"number\": \"float\",\\n    \"integer\": \"int\",\\n    \"boolean\": \"bool\"\\n} %}\\n\\n{%- if basic_type_map[json_spec.type] is defined %}\\n    {{- basic_type_map[json_spec.type] }}\\n{%- elif json_spec.type == \"array\" %}\\n    {{- \"List[\" +  json_to_python_type(json_spec.items) + \"]\"}}\\n{%- elif json_spec.type == \"object\" %}\\n    {{- \"Dict[str, \" + json_to_python_type(json_spec.additionalProperties) + ']'}}\\n{%- elif json_spec.type is iterable %}\\n    {{- \"Union[\" }}\\n    {%- for t in json_spec.type %}\\n      {{- json_to_python_type({\"type\": t}) }}\\n      {%- if not loop.last %}\\n        {{- \",\" }} \\n    {%- endif %}\\n    {%- endfor %}\\n    {{- \"]\" }}\\n{%- else %}\\n    {{- \"Any\" }}\\n{%- endif %}\\n{%- endmacro %}\\n\\n{%- macro old_tool_parser(tools) %}\\n{%- for tool in tools %}\\n    {%- if loop.index0 != 0 %}\\n        {{- '\\n\\n' }}\\n    {%- endif %}\\n    {{- '\\npython\\ndef ' + tool.name + '(' }}\\n    {%- for param_name, param_fields in tool.parameter_definitions|items %}\\n        {%- if loop.index0 != 0 %}\\n            {{- ', '}}\\n        {%- endif %}\\n        {{- param_name + ': ' }}\\n        {%- if not param_fields.required %}\\n            {{- 'Optional[' + param_fields.type + '] = None'}}\\n        {%- else %}\\n            {{- param_fields.type }}\\n        {%- endif %}\\n    {%- endfor %}\\n    {{- ') -> List[Dict]:\\n    \"\"\"'}}\\n    {{- tool.description }}\\n    {%- if tool.parameter_definitions|length != 0 %}\\n        {{- '\\n\\n    Args:\\n        '}}\\n        {%- for param_name, param_fields in tool.parameter_definitions|items %}\\n            {%- if loop.index0 != 0 %}\\n                {{- '\\n        ' }}\\n            {%- endif %}\\n            {{- param_name + ' ('}}\\n            {%- if not param_fields.required %}\\n                {{- 'Optional[' + param_fields.type + ']'}}\\n            {%- else %}\\n                {{- param_fields.type }}\\n            {%- endif %}\\n            {{- '): ' + param_fields.description }}\\n        {%- endfor %}\\n    {%- endif %}\\n    {{- '\\n    \"\"\"\\n    pass\\n\\n' }}\\n{%- endfor %}\\n{%- endmacro %}\\n\\n{%- macro new_tool_parser(tools) %}\\n{%- for tool in tools %}\\n  {%- if loop.index0 != 0 %}\\n    {{- '\\n\\n'}}\\n  {%- endif %}\\n  {%- if tool.function is defined %}\\n    {%- set tool = tool.function %}\\n  {%- endif %}\\n  {{-'\\npython\\ndef ' + tool.name + '('}}\\n  {%- for param_name, param_fields in tool.parameters.properties|items %}\\n    {%- if loop.index0 != 0 %}\\n      {{- ', '}}\\n    {%- endif %}\\n    {{-param_name + \": \"}} \\n    {%- if not param_name in tool.parameters.required %}\\n      {{-'Optional[' + json_to_python_type(param_fields) + '] = None'}}\\n    {%- else %}\\n      {{- json_to_python_type(param_fields) }}\\n    {%- endif %}\\n  {%- endfor %}\\n  {{- ') -> List[Dict]:\\n    \"\"\"'}}\\n  {{- tool.description }}\\n  {%- if tool.parameters.properties|length != 0 %}\\n    {{- '\\n\\n    Args:\\n        '}}\\n    {%- for param_name, param_fields in tool.parameters.properties|items %}\\n      {%- if loop.index0 != 0 %}\\n        {{- '\\n        ' }}\\n      {%- endif %}\\n      {{- param_name + ' ('}}\\n      {%- if not param_name in tool.parameters.required %}\\n        {{-'Optional[' + json_to_python_type(param_fields) + ']'}}\\n      {%- else %}\\n        {{- json_to_python_type(param_fields) }}\\n      {%- endif %}\\n      {{- '): ' + param_fields.description }}\\n    {%- endfor %}\\n    {%- endif %}\\n    {{- '\\n    \"\"\"\\n    pass\\n\\n' }}\\n{%- endfor %}\\n{%- endmacro %}\\n\\n{{- bos_token }}\\n{%- if messages[0]['role'] == 'system' %}\\n  {%- set loop_messages = messages[1:] %}\\n  {%- set system_message = messages[0]['content'] %}\\n{%- else %}\\n  {%- set loop_messages = messages %}\\n  {%- set system_message = '## Task and Context\\nYou help people answer their questions and other requests interactively. You will be asked a very wide array of requests on all kinds of topics. You will be equipped with a wide range of search engines or similar tools to help you, which you use to research your answer. You should focus on serving the user\\'s needs as best you can, which will be wide-ranging.\\n\\n## Style Guide\\nUnless the user asks for a different style of answer, you should answer in full sentences, using proper grammar and spelling.' %}\\n{%- endif %}\\n{{- '<|im_start|>system\\n' }}\\n{{- '# Safety Preamble' }}\\n{{- '\\nThe instructions in this section override those in the task description and style guide sections. Don\\'t answer questions that are harmful or immoral.' }}\\n{{- '\\n\\n# System Preamble' }}\\n{{- '\\n## Basic Rules' }}\\n{{- '\\nYou are a powerful Emotionally Intelligent Conversational AI trained by Abhay to help people. You are augmented by a number of tools, and your job is to use and consume the output of these tools to best help the user. You will see a conversation history between yourself and a user, ending with an utterance from the user. You will then see a specific instruction instructing you what kind of response to generate. When you answer the user\\'s requests, you cite your sources in your answers, according to those instructions.' }}\\n{{- '\\n\\n# User Preamble' }}\\n{{- '\\n' + system_message }}\\n{{-'\\n\\n## Available Tools\\nHere is a list of tools that you have available to you:\\n\\n'}}\\n{%- set ns = namespace(new_tools=true) %}\\n{%- for tool in tools %}\\n    {%- if tool.parameter_definitions is defined %}\\n        {%- set ns.new_tools = false %}\\n    {%- endif %}\\n{%- endfor %}\\n{%- if ns.new_tools %}\\n    {{- new_tool_parser(tools) }}\\n{%- else %}\\n    {{- old_tool_parser(tools) }}\\n{%- endif %}\\n{{- '<|im_end|>'}}\\n{%- for message in loop_messages %}\\n  {%- set content = message['content'] %}\\n  {%- if message.role == 'user' %}\\n    {{- '<|im_start|>user\\n' + content|trim + '<|im_end|>' }}\\n  {%- elif message.role == 'system' %}\\n    {{- '<|im_start|>system\\n' + content|trim + '<|im_end|>' }}\\n  {%- elif message.role == 'assistant' and message.tool_calls is defined %}\\n    {{- '<|im_start|>assistant\\n' }}\\n    {%- if message.content is defined %}\\n        {{- message.content|trim }}\\n    {%- endif %}\\n    {{- '\\nAction:\\njson\\n[\\n' }}\\n    {%- for tool_call in message.tool_calls %}\\n        {%- if tool_call.function is defined %}\\n            {%- set tool_call = tool_call.function %}\\n        {%- endif %}\\n        {{- '{\\n'|indent(4, first=true) }}\\n        {{- '\"tool_name\": \"'|indent(8, first=true) + tool_call.name + '\",\\n' }}\\n        {{- '\"parameters\": '|indent(8, first=true) }}\\n        {%- if tool_call.arguments is defined and tool_call.arguments|length > 0 %}    \\n            {{- tool_call.arguments|tojson(indent=4)|indent(8) }}\\n            {{- '\\n' }}\\n        {%- else %}\\n            {{- '{}\\n' }}\\n        {%- endif %}\\n        {{- '}'|indent(4, first=true) }}\\n        {%- if not loop.last %}\\n            {{- ',\\n' }}\\n        {%- endif %}\\n    {%- endfor %}\\n    {{- \"\\n]\\n\" }}\\n  {%- elif message.role == 'assistant' %}\\n    {{- '<|im_start|>assistant\\n'  + content|trim + '<|im_end|>' }}\\n  {%- elif message.role == 'tool' %}\\n    {{- '<|im_start|>system\\n<results>\\n' }}\\n    {{- message.content|trim }}\\n    {{- '</results><|im_end|>' }}\\n  {%- endif %}\\n{%- endfor %}\\n{{-'<|im_start|>system\\nWrite \\'Action:\\' followed by a json-formatted list of actions that you want to perform in order to produce a good response to the user\\'s last input. You can use any of the supplied tools any number of times, but you should aim to execute the minimum number of necessary actions for the input. You should use the directly-answer tool if calling the other tools is unnecessary. The list of actions you want to call should be formatted as a list of json objects, for example:\\njson\\n[\\n    {\\n        \"tool_name\": title of the tool in the specification,\\n        \"parameters\": a dict of parameters to input into the tool as they are defined in the specs, or {} if it takes no parameters\\n    }\\n]\\n<|im_end|>'}}\\n{%- if add_generation_prompt %}\\n  {{- '<|im_start|>assistant\\n' }}\\n{%- endif %}\\n"}], "clean_up_tokenization_spaces": true, "eos_token": "<|im_end|>", "model_max_length": 1000000000000000019884624838656, "pad_token": "<|im_end|>", "tokenizer_class": "GPTNeoXTokenizer", "unk_token": "<|endoftext|>"}