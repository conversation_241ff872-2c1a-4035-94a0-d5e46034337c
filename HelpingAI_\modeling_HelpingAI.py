""" HelpingAI model with enhanced architecture inspired by Qwen3. """
from typing import Op<PERSON>, Tuple, Union, List
import math
import warnings

import torch
import torch.utils.checkpoint
from transformers import AutoModel, AutoModelForCausalLM
from torch import nn
from torch.nn import CrossEntropyLoss
from transformers.modeling_outputs import (
    BaseModelOutputWithPast,
    CausalLMOutputWithPast,
)
from transformers.modeling_utils import PreTrainedModel
from transformers.utils import logging
from transformers.cache_utils import Cache, DynamicCache
try:
    from .configuration_HelpingAI import HelpingAIConfig
except ImportError:
    # Handle case when imported directly
    from configuration_HelpingAI import HelpingAIConfig


logger = logging.get_logger(__name__)


class HelpingAIRMSNorm(nn.Module):
    """Enhanced RMS Normalization layer."""

    def __init__(self, hidden_size: int, eps: float = 1e-6):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(hidden_size))
        self.variance_epsilon = eps

    def forward(self, hidden_states: torch.Tensor):
        input_dtype = hidden_states.dtype
        hidden_states = hidden_states.to(torch.float32)
        variance = hidden_states.pow(2).mean(-1, keepdim=True)
        hidden_states = hidden_states * torch.rsqrt(variance + self.variance_epsilon)
        return self.weight * hidden_states.to(input_dtype)


def _make_causal_mask(
    input_ids_shape: torch.Size,
    dtype: torch.dtype,
    device: torch.device,
    past_key_values_length: int = 0,
    sliding_window: Optional[int] = None,
):
    """
    Enhanced causal mask with optional sliding window support.
    """
    batch_size, tgt_len = input_ids_shape
    mask = torch.full((tgt_len, tgt_len), torch.finfo(dtype).min, device=device)
    mask_cond = torch.arange(mask.size(-1), device=device)
    mask.masked_fill_(mask_cond < (mask_cond + 1).view(mask.size(-1), 1), 0)

    # Apply sliding window if specified
    if sliding_window is not None:
        diagonal = torch.arange(tgt_len, device=device)
        context_mask = mask_cond[None, :] > (diagonal[:, None] + sliding_window)
        mask.masked_fill_(context_mask, torch.finfo(dtype).min)

    mask = mask.to(dtype)
    if past_key_values_length > 0:
        mask = torch.cat([torch.zeros(tgt_len, past_key_values_length, dtype=dtype, device=device), mask], dim=-1)
    return mask[None, None, :, :].expand(batch_size, 1, tgt_len, tgt_len + past_key_values_length)


def _expand_mask(mask: torch.Tensor, dtype: torch.dtype, tgt_len: Optional[int] = None):
    """Expands attention_mask from `[batch_size, seq_len]` to `[batch_size, 1, tgt_seq_len, src_seq_len]`."""
    batch_size, src_len = mask.size()
    tgt_len = tgt_len if tgt_len is not None else src_len

    expanded_mask = mask[:, None, None, :].expand(batch_size, 1, tgt_len, src_len).to(dtype)
    inverted_mask = 1.0 - expanded_mask

    return inverted_mask.masked_fill(
        inverted_mask.to(torch.bool), torch.finfo(dtype).min
    )


class HelpingAIRotaryEmbedding(nn.Module):
    """Enhanced Rotary Position Embedding with advanced scaling support."""

    def __init__(
        self,
        dim: int,
        max_position_embeddings: int = 2048,
        base: float = 10000.0,
        device: Optional[torch.device] = None,
        scaling_factor: float = 1.0,
    ):
        super().__init__()
        self.dim = dim
        self.max_position_embeddings = max_position_embeddings
        self.base = base
        self.scaling_factor = scaling_factor

        # Calculate inverse frequencies
        inv_freq = 1.0 / (
            self.base ** (torch.arange(0, self.dim, 2, dtype=torch.int64).float() / self.dim)
        )
        self.register_buffer("inv_freq", inv_freq, persistent=False)

        # Initialize cache
        self._set_cos_sin_cache(
            seq_len=max_position_embeddings,
            device=device or inv_freq.device,
            dtype=torch.get_default_dtype(),
        )

    def _set_cos_sin_cache(self, seq_len: int, device: torch.device, dtype: torch.dtype):
        self.max_seq_len_cached = seq_len
        t = torch.arange(self.max_seq_len_cached, device=device, dtype=self.inv_freq.dtype)
        t = t / self.scaling_factor

        freqs = torch.outer(t, self.inv_freq)
        # Different from paper, but it uses a different permutation in order to obtain the same calculation
        emb = torch.cat((freqs, freqs), dim=-1)
        self.register_buffer("cos_cached", emb.cos().to(dtype), persistent=False)
        self.register_buffer("sin_cached", emb.sin().to(dtype), persistent=False)

    def forward(self, x: torch.Tensor, position_ids: Optional[torch.LongTensor] = None):
        if position_ids is None:
            # x: [batch_size, num_heads, seq_len, head_dim]
            seq_len = x.shape[-2]
            if seq_len > self.max_seq_len_cached:
                self._set_cos_sin_cache(seq_len=seq_len, device=x.device, dtype=x.dtype)
            return (
                self.cos_cached[:seq_len].to(dtype=x.dtype),
                self.sin_cached[:seq_len].to(dtype=x.dtype),
            )
        else:
            seq_len = torch.max(position_ids) + 1
            if seq_len > self.max_seq_len_cached:
                self._set_cos_sin_cache(seq_len=seq_len, device=x.device, dtype=x.dtype)
            return (
                self.cos_cached[position_ids].to(dtype=x.dtype),
                self.sin_cached[position_ids].to(dtype=x.dtype),
            )


class HelpingAILinearScalingRotaryEmbedding(HelpingAIRotaryEmbedding):
    """Linear scaling variant of RoPE."""

    def __init__(self, dim, max_position_embeddings=2048, base=10000, device=None, scaling_factor=1.0):
        self.scaling_factor = scaling_factor
        super().__init__(dim, max_position_embeddings, base, device, scaling_factor)


class HelpingAIDynamicNTKScalingRotaryEmbedding(HelpingAIRotaryEmbedding):
    """Dynamic NTK scaling variant of RoPE."""

    def __init__(self, dim, max_position_embeddings=2048, base=10000, device=None, scaling_factor=1.0):
        self.scaling_factor = scaling_factor
        super().__init__(dim, max_position_embeddings, base, device)

    def _set_cos_sin_cache(self, seq_len: int, device: torch.device, dtype: torch.dtype):
        self.max_seq_len_cached = seq_len

        if seq_len > self.max_position_embeddings:
            base = self.base * (
                (self.scaling_factor * seq_len / self.max_position_embeddings) - (self.scaling_factor - 1)
            ) ** (self.dim / (self.dim - 2))
            inv_freq = 1.0 / (
                base ** (torch.arange(0, self.dim, 2, dtype=torch.float32).to(device) / self.dim)
            )
        else:
            inv_freq = self.inv_freq

        t = torch.arange(self.max_seq_len_cached, device=device, dtype=inv_freq.dtype)
        freqs = torch.outer(t, inv_freq)
        emb = torch.cat((freqs, freqs), dim=-1)
        self.register_buffer("cos_cached", emb.cos().to(dtype), persistent=False)
        self.register_buffer("sin_cached", emb.sin().to(dtype), persistent=False)


def rotate_half(x: torch.Tensor):
    """Rotates half the hidden dims of the input."""
    x1, x2 = torch.chunk(x, 2, dim=-1)
    return torch.cat((-x2, x1), dim=-1)


def apply_rotary_pos_emb(q, k, cos, sin, position_ids=None, unsqueeze_dim=1):
    """Enhanced rotary position embedding application."""
    cos = cos.unsqueeze(unsqueeze_dim)
    sin = sin.unsqueeze(unsqueeze_dim)

    if position_ids is not None:
        cos = cos[position_ids]
        sin = sin[position_ids]

    q_embed = (q * cos) + (rotate_half(q) * sin)
    k_embed = (k * cos) + (rotate_half(k) * sin)
    return q_embed, k_embed


def _get_rope_embedding(config: HelpingAIConfig, device: torch.device) -> nn.Module:
    """Factory function to create appropriate RoPE embedding based on config."""
    rope_scaling = getattr(config, "rope_scaling", None)

    if rope_scaling is None:
        return HelpingAIRotaryEmbedding(
            config.head_dim,
            max_position_embeddings=config.max_position_embeddings,
            base=config.rope_theta,
            device=device,
        )

    scaling_type = rope_scaling.get("type", "default")
    scaling_factor = rope_scaling.get("factor", 1.0)

    if scaling_type == "linear":
        return HelpingAILinearScalingRotaryEmbedding(
            config.head_dim,
            max_position_embeddings=config.max_position_embeddings,
            base=config.rope_theta,
            device=device,
            scaling_factor=scaling_factor,
        )
    elif scaling_type == "dynamic":
        return HelpingAIDynamicNTKScalingRotaryEmbedding(
            config.head_dim,
            max_position_embeddings=config.max_position_embeddings,
            base=config.rope_theta,
            device=device,
            scaling_factor=scaling_factor,
        )
    else:
        # Default to standard RoPE for unsupported types
        return HelpingAIRotaryEmbedding(
            config.head_dim,
            max_position_embeddings=config.max_position_embeddings,
            base=config.rope_theta,
            device=device,
        )


class HelpingAIMLP(nn.Module):
    """Enhanced MLP with SwiGLU activation."""

    def __init__(self, config: HelpingAIConfig):
        super().__init__()
        self.config = config
        self.hidden_size = config.hidden_size
        self.intermediate_size = config.intermediate_size

        # Enhanced: Support for attention bias in MLP
        bias = getattr(config, 'attention_bias', False)
        self.gate_proj = nn.Linear(config.hidden_size, config.intermediate_size, bias=bias)
        self.up_proj = nn.Linear(config.hidden_size, config.intermediate_size, bias=bias)
        self.down_proj = nn.Linear(config.intermediate_size, config.hidden_size, bias=bias)

        # Enhanced: Configurable activation function
        if config.hidden_act == "silu":
            self.act_fn = nn.SiLU()
        elif config.hidden_act == "gelu":
            self.act_fn = nn.GELU()
        elif config.hidden_act == "relu":
            self.act_fn = nn.ReLU()
        else:
            self.act_fn = nn.SiLU()  # Default fallback

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.down_proj(self.act_fn(self.gate_proj(x)) * self.up_proj(x))


class HelpingAIMoEGate(nn.Module):
    """Enhanced MoE gating mechanism with improved routing."""

    def __init__(self, config: HelpingAIConfig):
        super().__init__()
        self.config = config
        self.num_experts = config.num_local_experts
        self.num_experts_per_tok = config.num_experts_per_tok
        self.gate = nn.Linear(config.hidden_size, config.num_local_experts, bias=False)

    def forward(self, hidden_states: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        batch_size, seq_len, hidden_dim = hidden_states.shape
        hidden_states = hidden_states.view(-1, hidden_dim)

        # Compute gating scores
        gate_logits = self.gate(hidden_states)

        # Get top-k experts
        weights, selected_experts = torch.topk(gate_logits, self.num_experts_per_tok, dim=-1)
        weights = nn.functional.softmax(weights, dim=-1, dtype=torch.float).to(hidden_states.dtype)

        # Calculate load balancing loss
        if self.training:
            # Auxiliary loss for load balancing
            aux_loss = self._compute_aux_loss(gate_logits, selected_experts)
        else:
            aux_loss = torch.tensor(0.0, device=hidden_states.device, dtype=hidden_states.dtype)

        return weights, selected_experts, aux_loss

    def _compute_aux_loss(self, gate_logits: torch.Tensor, selected_experts: torch.Tensor) -> torch.Tensor:
        """Compute auxiliary loss for load balancing."""
        # Compute the probability of each expert being selected
        probs = nn.functional.softmax(gate_logits, dim=-1)

        # Compute the fraction of tokens assigned to each expert
        expert_counts = torch.zeros_like(probs)
        expert_counts.scatter_add_(1, selected_experts, torch.ones_like(selected_experts, dtype=probs.dtype))
        expert_fractions = expert_counts.mean(dim=0)

        # Compute the mean probability for each expert
        expert_probs = probs.mean(dim=0)

        # Auxiliary loss encourages uniform distribution
        aux_loss = (expert_fractions * expert_probs).sum() * self.num_experts
        return aux_loss


class HelpingAIMoE(nn.Module):
    """Enhanced Mixture of Experts implementation."""

    def __init__(self, config: HelpingAIConfig):
        super().__init__()
        self.config = config
        self.num_experts = config.num_local_experts
        self.num_experts_per_tok = config.num_experts_per_tok
        self.router_aux_loss_coef = config.router_aux_loss_coef

        # Create experts
        self.experts = nn.ModuleList([HelpingAIMLP(config) for _ in range(self.num_experts)])
        self.gate = HelpingAIMoEGate(config)

    def forward(self, hidden_states: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        batch_size, seq_len, hidden_dim = hidden_states.shape
        hidden_states = hidden_states.view(-1, hidden_dim)

        # Get routing weights and selected experts
        weights, selected_experts, aux_loss = self.gate(hidden_states)

        # Initialize output
        final_hidden_states = torch.zeros_like(hidden_states)

        # Process tokens through selected experts
        for i in range(self.num_experts_per_tok):
            expert_idx = selected_experts[:, i]
            expert_weight = weights[:, i:i+1]

            # Group tokens by expert
            for expert_id in range(self.num_experts):
                expert_mask = (expert_idx == expert_id)
                if expert_mask.any():
                    expert_tokens = hidden_states[expert_mask]
                    expert_output = self.experts[expert_id](expert_tokens)
                    final_hidden_states[expert_mask] += expert_weight[expert_mask] * expert_output

        final_hidden_states = final_hidden_states.view(batch_size, seq_len, hidden_dim)
        return final_hidden_states, aux_loss


# Legacy alias for backward compatibility
MLP = HelpingAIMLP


def repeat_kv(hidden_states: torch.Tensor, n_rep: int) -> torch.Tensor:
    """
    This is the equivalent of torch.repeat_interleave(x, dim=1, repeats=n_rep). The hidden states go from (batch,
    num_key_value_heads, seqlen, head_dim) to (batch, num_attention_heads, seqlen, head_dim)
    """
    batch, num_key_value_heads, slen, head_dim = hidden_states.shape
    if n_rep == 1:
        return hidden_states
    hidden_states = hidden_states[:, :, None, :, :].expand(batch, num_key_value_heads, n_rep, slen, head_dim)
    return hidden_states.reshape(batch, num_key_value_heads * n_rep, slen, head_dim)


class HelpingAIAttention(nn.Module):
    """Enhanced Multi-Head Attention with GQA support and sliding window."""

    def __init__(self, config: HelpingAIConfig, layer_idx: Optional[int] = None):
        super().__init__()
        self.config = config
        self.layer_idx = layer_idx
        self.hidden_size = config.hidden_size
        self.num_heads = config.num_attention_heads
        self.head_dim = getattr(config, 'head_dim', self.hidden_size // self.num_heads)
        self.num_key_value_heads = getattr(config, 'num_key_value_heads', self.num_heads)
        self.num_key_value_groups = self.num_heads // self.num_key_value_heads
        self.max_position_embeddings = config.max_position_embeddings
        self.attention_dropout = config.attention_dropout

        # Sliding window configuration
        self.use_sliding_window = getattr(config, 'use_sliding_window', False)
        self.sliding_window = getattr(config, 'sliding_window', 4096)
        self.max_window_layers = getattr(config, 'max_window_layers', 28)

        # Determine if this layer should use sliding window
        self.is_sliding_window = (
            self.use_sliding_window and
            layer_idx is not None and
            layer_idx >= self.max_window_layers
        )

        if (self.head_dim * self.num_heads) != self.hidden_size:
            raise ValueError(
                f"hidden_size must be divisible by num_heads (got `hidden_size`: {self.hidden_size}"
                f" and `num_heads`: {self.num_heads})."
            )

        # Enhanced: Support for attention bias
        bias = getattr(config, 'attention_bias', False)
        self.q_proj = nn.Linear(self.hidden_size, self.num_heads * self.head_dim, bias=bias)
        self.k_proj = nn.Linear(self.hidden_size, self.num_key_value_heads * self.head_dim, bias=bias)
        self.v_proj = nn.Linear(self.hidden_size, self.num_key_value_heads * self.head_dim, bias=bias)
        self.o_proj = nn.Linear(self.hidden_size, self.hidden_size, bias=bias)

        self._init_rope()

    def _init_rope(self):
        # Enhanced: Use partial rotary factor or calculate from rope_pct
        if hasattr(self.config, 'partial_rotary_factor'):
            self.rotary_ndims = int(self.head_dim * self.config.partial_rotary_factor)
        else:
            self.rotary_ndims = int(self.head_dim * getattr(self.config, 'rope_pct', 1.0))

        # Use the factory function to get appropriate RoPE embedding
        self.rotary_emb = _get_rope_embedding(self.config, device=None)

    def forward(
        self,
        hidden_states: torch.FloatTensor,
        attention_mask: Optional[torch.FloatTensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_value: Optional[Cache] = None,
        output_attentions: Optional[bool] = False,
        use_cache: Optional[bool] = False,
        cache_position: Optional[torch.LongTensor] = None,
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor], Optional[Cache]]:
        bsz, q_len, _ = hidden_states.size()

        query_states = self.q_proj(hidden_states)
        key_states = self.k_proj(hidden_states)
        value_states = self.v_proj(hidden_states)

        query_states = query_states.view(bsz, q_len, self.num_heads, self.head_dim).transpose(1, 2)
        key_states = key_states.view(bsz, q_len, self.num_key_value_heads, self.head_dim).transpose(1, 2)
        value_states = value_states.view(bsz, q_len, self.num_key_value_heads, self.head_dim).transpose(1, 2)

        # Enhanced: Apply RoPE to partial dimensions
        cos, sin = None, None
        if self.rotary_ndims > 0:
            query_rot = query_states[..., : self.rotary_ndims]
            query_pass = query_states[..., self.rotary_ndims :]
            key_rot = key_states[..., : self.rotary_ndims]
            key_pass = key_states[..., self.rotary_ndims :]

            cos, sin = self.rotary_emb(query_rot, position_ids)
            query_rot, key_rot = apply_rotary_pos_emb(query_rot, key_rot, cos, sin, position_ids)

            query_states = torch.cat((query_rot, query_pass), dim=-1)
            key_states = torch.cat((key_rot, key_pass), dim=-1)

        # Enhanced: Handle caching with new Cache interface
        if past_key_value is not None:
            cache_kwargs = {"sin": sin, "cos": cos} if self.rotary_ndims > 0 else {}
            key_states, value_states = past_key_value.update(key_states, value_states, self.layer_idx, cache_kwargs)

        # Repeat k/v heads if n_kv_heads < n_heads (GQA)
        key_states = repeat_kv(key_states, self.num_key_value_groups)
        value_states = repeat_kv(value_states, self.num_key_value_groups)

        # Enhanced: Apply sliding window mask if configured
        attn_weights = torch.matmul(query_states, key_states.transpose(2, 3)) / math.sqrt(self.head_dim)

        if attention_mask is not None:
            causal_mask = attention_mask
            if self.is_sliding_window and causal_mask.size(-1) > self.sliding_window:
                # Apply sliding window mask
                sliding_window_mask = _make_causal_mask(
                    (bsz, q_len),
                    dtype=attn_weights.dtype,
                    device=attn_weights.device,
                    past_key_values_length=0,
                    sliding_window=self.sliding_window,
                )
                causal_mask = torch.maximum(causal_mask, sliding_window_mask)

            attn_weights = attn_weights + causal_mask

        # Enhanced: Apply attention dropout
        attn_weights = nn.functional.softmax(attn_weights, dim=-1, dtype=torch.float32).to(query_states.dtype)
        if self.attention_dropout > 0.0:
            attn_weights = nn.functional.dropout(attn_weights, p=self.attention_dropout, training=self.training)

        attn_output = torch.matmul(attn_weights, value_states)

        # Merge heads
        attn_output = attn_output.transpose(1, 2).contiguous()
        attn_output = attn_output.reshape(bsz, q_len, self.hidden_size)

        # Final linear projection
        attn_output = self.o_proj(attn_output)

        if not output_attentions:
            attn_weights = None

        return attn_output, attn_weights, past_key_value


class HelpingAIDecoderLayer(nn.Module):
    """Enhanced decoder layer with RMS normalization and improved attention."""

    def __init__(self, config: HelpingAIConfig, layer_idx: int):
        super().__init__()
        self.hidden_size = config.hidden_size
        self.layer_idx = layer_idx

        self.self_attn = HelpingAIAttention(config, layer_idx=layer_idx)
        self.mlp = MLP(config)

        # Enhanced: Use RMS normalization instead of LayerNorm
        self.input_layernorm = HelpingAIRMSNorm(config.hidden_size, eps=config.rms_norm_eps)
        self.post_attention_layernorm = HelpingAIRMSNorm(config.hidden_size, eps=config.rms_norm_eps)

    def forward(
        self,
        hidden_states: torch.FloatTensor,
        attention_mask: Optional[torch.FloatTensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_value: Optional[Cache] = None,
        output_attentions: Optional[bool] = False,
        use_cache: Optional[bool] = False,
        cache_position: Optional[torch.LongTensor] = None,
    ) -> Tuple[torch.FloatTensor, Optional[torch.FloatTensor], Optional[Cache]]:
        """Enhanced forward pass with improved residual connections."""
        residual = hidden_states
        hidden_states = self.input_layernorm(hidden_states)

        # Self Attention
        hidden_states, self_attn_weights, present_key_value = self.self_attn(
            hidden_states=hidden_states,
            attention_mask=attention_mask,
            position_ids=position_ids,
            past_key_value=past_key_value,
            output_attentions=output_attentions,
            use_cache=use_cache,
            cache_position=cache_position,
        )
        hidden_states = residual + hidden_states

        # Fully Connected
        residual = hidden_states
        hidden_states = self.post_attention_layernorm(hidden_states)
        hidden_states = self.mlp(hidden_states)
        hidden_states = residual + hidden_states

        outputs = (hidden_states,)

        if output_attentions:
            outputs += (self_attn_weights,)

        if use_cache:
            outputs += (present_key_value,)

        return outputs


class HelpingAIPreTrainedModel(PreTrainedModel):
    """An abstract class to handle weights initialization and a simple interface
    for downloading and loading pretrained models.
    """

    config_class = HelpingAIConfig
    base_model_prefix = "transformer"
    supports_gradient_checkpointing = True
    _no_split_modules = ["DecoderLayer"]
    _skip_keys_device_placement = "past_key_values"

    def _init_weights(self, module: nn.Module):
        """Initialize the weights"""
        if isinstance(module, nn.Linear):
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.bias is not None:
                module.bias.data.zero_()
        elif isinstance(module, nn.Embedding):
            module.weight.data.normal_(mean=0.0, std=self.config.initializer_range)
            if module.padding_idx is not None:
                module.weight.data[module.padding_idx].zero_()
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)

    def _set_gradient_checkpointing(self, module: nn.Module, value=False):
        if isinstance(module, HelpingAIModel):
            module.gradient_checkpointing = value


class HelpingAIModel(HelpingAIPreTrainedModel):
    """Enhanced HelpingAI model with improved architecture."""

    def __init__(self, config: HelpingAIConfig):
        super().__init__(config)
        self.padding_idx = config.pad_token_id
        self.vocab_size = config.vocab_size

        self.embed_tokens = nn.Embedding(config.vocab_size, config.hidden_size, config.pad_token_id)

        # Enhanced: Use improved decoder layers with layer indices
        self.layers = nn.ModuleList([
            HelpingAIDecoderLayer(config, layer_idx)
            for layer_idx in range(config.num_hidden_layers)
        ])

        # Enhanced: Use RMS normalization
        self.norm = HelpingAIRMSNorm(config.hidden_size, eps=config.rms_norm_eps)

        self.gradient_checkpointing = False
        # Initialize weights and apply final processing
        self.post_init()

    def get_input_embeddings(self):
        return self.embed_tokens

    def set_input_embeddings(self, value: nn.Module):
        self.embed_tokens = value

    def _prepare_decoder_attention_mask(
        self,
        attention_mask: torch.Tensor,
        input_shape: torch.Size,
        inputs_embeds: torch.Tensor,
        past_key_values_length: int,
        sliding_window: Optional[int] = None,
    ):
        """Enhanced attention mask preparation with sliding window support."""
        # Create causal mask
        # [batch_size, seq_len] -> [batch_size, 1, tgt_seq_len, src_seq_len]
        combined_attention_mask = None
        if input_shape[-1] > 1:
            combined_attention_mask = _make_causal_mask(
                input_shape,
                inputs_embeds.dtype,
                device=inputs_embeds.device,
                past_key_values_length=past_key_values_length,
                sliding_window=sliding_window,
            )

        if attention_mask is not None:
            # [batch_size, seq_len] -> [batch_size, 1, tgt_seq_len, src_seq_len]
            expanded_attn_mask = _expand_mask(
                attention_mask, inputs_embeds.dtype, tgt_len=input_shape[-1]
            ).to(inputs_embeds.device)
            combined_attention_mask = expanded_attn_mask if combined_attention_mask is None else expanded_attn_mask + combined_attention_mask

        return combined_attention_mask

    def forward(
        self,
        input_ids: Optional[torch.LongTensor] = None,
        attention_mask: Optional[torch.FloatTensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_values: Optional[Cache] = None,
        inputs_embeds: Optional[torch.FloatTensor] = None,
        use_cache: Optional[bool] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
        cache_position: Optional[torch.LongTensor] = None,
    ) -> Union[Tuple, BaseModelOutputWithPast]:
        output_attentions = output_attentions if output_attentions is not None else self.config.output_attentions
        output_hidden_states = output_hidden_states if output_hidden_states is not None else self.config.output_hidden_states
        use_cache = use_cache if use_cache is not None else self.config.use_cache

        return_dict = return_dict if return_dict is not None else self.config.use_return_dict

        # Retrieve input_ids and inputs_embeds
        if input_ids is not None and inputs_embeds is not None:
            raise ValueError(
                "You cannot specify both decoder_input_ids and decoder_inputs_embeds at the same time"
            )
        elif input_ids is not None:
            batch_size, seq_length = input_ids.shape
        elif inputs_embeds is not None:
            batch_size, seq_length, _ = inputs_embeds.shape
        else:
            raise ValueError(
                "You have to specify either decoder_input_ids or decoder_inputs_embeds"
            )

        # Enhanced: Handle new Cache interface
        if use_cache and past_key_values is None:
            past_key_values = DynamicCache()

        seq_length_with_past = seq_length
        past_key_values_length = 0

        if past_key_values is not None:
            if isinstance(past_key_values, Cache):
                past_key_values_length = past_key_values.get_seq_length()
            else:
                # Legacy tuple format
                past_key_values_length = past_key_values[0][0].shape[2] if past_key_values[0] is not None else 0
            seq_length_with_past = seq_length_with_past + past_key_values_length

        if position_ids is None:
            device = input_ids.device if input_ids is not None else inputs_embeds.device
            position_ids = torch.arange(
                past_key_values_length,
                seq_length + past_key_values_length,
                dtype=torch.long,
                device=device,
            )
            position_ids = position_ids.unsqueeze(0)

        if inputs_embeds is None:
            inputs_embeds = self.embed_tokens(input_ids)

        # Enhanced: Prepare attention mask with sliding window support
        if attention_mask is None:
            attention_mask = torch.ones(
                (batch_size, seq_length_with_past),
                dtype=torch.bool,
                device=inputs_embeds.device,
            )

        # Check if any layer uses sliding window
        use_sliding_window = getattr(self.config, 'use_sliding_window', False)
        sliding_window = getattr(self.config, 'sliding_window', None) if use_sliding_window else None

        attention_mask = self._prepare_decoder_attention_mask(
            attention_mask,
            (batch_size, seq_length),
            inputs_embeds,
            past_key_values_length,
            sliding_window=sliding_window,
        )

        hidden_states = inputs_embeds

        if self.gradient_checkpointing and self.training:
            if use_cache:
                logger.warning(
                    "`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`..."
                )
                use_cache = False

        # Decoder layers
        all_hidden_states = () if output_hidden_states else None
        all_self_attns = () if output_attentions else None

        for decoder_layer in self.layers:
            if output_hidden_states:
                all_hidden_states += (hidden_states,)

            if self.gradient_checkpointing and self.training:
                def create_custom_forward(module):
                    def custom_forward(*inputs):
                        return module(*inputs, output_attentions, use_cache)
                    return custom_forward

                layer_outputs = torch.utils.checkpoint.checkpoint(
                    create_custom_forward(decoder_layer),
                    hidden_states,
                    attention_mask,
                    position_ids,
                    past_key_values,
                    cache_position,
                )
            else:
                layer_outputs = decoder_layer(
                    hidden_states,
                    attention_mask=attention_mask,
                    position_ids=position_ids,
                    past_key_value=past_key_values,
                    output_attentions=output_attentions,
                    use_cache=use_cache,
                    cache_position=cache_position,
                )

            hidden_states = layer_outputs[0]

            if output_attentions:
                all_self_attns += (layer_outputs[1],)

        hidden_states = self.norm(hidden_states)

        # Add hidden states from the last decoder layer
        if output_hidden_states:
            all_hidden_states += (hidden_states,)

        next_cache = past_key_values if use_cache else None
        if not return_dict:
            return tuple(
                v
                for v in [hidden_states, next_cache, all_hidden_states, all_self_attns]
                if v is not None
            )
        return BaseModelOutputWithPast(
            last_hidden_state=hidden_states,
            past_key_values=next_cache,
            hidden_states=all_hidden_states,
            attentions=all_self_attns,
        )


class HelpingAIForCausalLM(HelpingAIPreTrainedModel):
    _tied_weights_keys = ["lm_head.weight"]

    def __init__(self, config: HelpingAIConfig):
        super().__init__(config)

        self.model = HelpingAIModel(config)
        self.lm_head = nn.Linear(config.hidden_size, config.vocab_size, bias=False)

        # Initialize weights and apply final processing
        self.post_init()

    def get_input_embeddings(self):
        return self.model.embed_tokens

    def set_input_embeddings(self, value):
        self.model.embed_tokens = value

    def get_output_embeddings(self):
        return self.lm_head

    def set_output_embeddings(self, new_embeddings: nn.Module):
        self.lm_head = new_embeddings

    def get_decoder(self):
        return self.transformer

    def set_decoder(self, decoder):
        self.transformer = decoder

    def forward(
        self,
        input_ids: Optional[torch.LongTensor] = None,
        attention_mask: Optional[torch.FloatTensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_values: Optional[Cache] = None,
        inputs_embeds: Optional[torch.FloatTensor] = None,
        labels: Optional[torch.LongTensor] = None,
        use_cache: Optional[bool] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
        cache_position: Optional[torch.LongTensor] = None,
    ) -> Union[Tuple, CausalLMOutputWithPast]:
        output_attentions = (
            output_attentions
            if output_attentions is not None
            else self.config.output_attentions
        )
        output_hidden_states = (
            output_hidden_states
            if output_hidden_states is not None
            else self.config.output_hidden_states
        )
        return_dict = (
            return_dict if return_dict is not None else self.config.use_return_dict
        )

        # decoder outputs consists of (dec_features, layer_state, dec_hidden, dec_attn)
        outputs = self.model(
            input_ids,
            attention_mask=attention_mask,
            position_ids=position_ids,
            past_key_values=past_key_values,
            inputs_embeds=inputs_embeds,
            use_cache=use_cache,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
            cache_position=cache_position,
        )

        hidden_states = outputs[0]
        logits = self.lm_head(hidden_states).float()

        loss = None
        if labels is not None:
            # Shift so that tokens < n predict n
            shift_logits = logits[..., :-1, :].contiguous()
            shift_labels = labels[..., 1:].contiguous()
            # Flatten the tokens
            loss_fct = CrossEntropyLoss()
            shift_logits = shift_logits.view(-1, self.config.vocab_size)
            shift_labels = shift_labels.view(-1)
            # Enable model parallelism
            shift_labels = shift_labels.to(shift_logits.device)
            loss = loss_fct(shift_logits, shift_labels)

        if not return_dict:
            output = (logits,) + outputs[1:]
            return (loss,) + output if loss is not None else output

        return CausalLMOutputWithPast(
            loss=loss,
            logits=logits,
            past_key_values=outputs.past_key_values,
            hidden_states=outputs.hidden_states,
            attentions=outputs.attentions,
        )

    def prepare_inputs_for_generation(
        self,
        input_ids,
        past_key_values: Optional[Cache] = None,
        attention_mask: Optional[torch.Tensor] = None,
        inputs_embeds: Optional[torch.Tensor] = None,
        cache_position: Optional[torch.LongTensor] = None,
        **kwargs,
    ):
        """Enhanced input preparation for generation with Cache support."""
        # Enhanced: Handle new Cache interface
        if past_key_values is not None:
            if isinstance(past_key_values, Cache):
                # With Cache, we only need the last token
                if cache_position is None:
                    past_length = past_key_values.get_seq_length()
                    cache_position = torch.arange(
                        past_length, past_length + input_ids.shape[1], device=input_ids.device
                    )
                if cache_position[-1] >= past_key_values.get_max_length():
                    cache_position = cache_position % past_key_values.get_max_length()
                input_ids = input_ids[:, -1:]
            else:
                # Legacy tuple format
                if past_key_values[0] is not None:
                    input_ids = input_ids[:, -1:]

        position_ids = kwargs.get("position_ids", None)
        if attention_mask is not None and position_ids is None:
            # Create position_ids on the fly for batch generation
            position_ids = attention_mask.long().cumsum(-1) - 1
            position_ids.masked_fill_(attention_mask == 0, 1)
            if past_key_values is not None:
                position_ids = position_ids[:, -1].unsqueeze(-1)

        # If `inputs_embeds` are passed, we only want to use them in the 1st generation step
        if inputs_embeds is not None and past_key_values is None:
            model_inputs = {"inputs_embeds": inputs_embeds}
        else:
            model_inputs = {"input_ids": input_ids}

        model_inputs.update(
            {
                "attention_mask": attention_mask,
                "past_key_values": past_key_values,
                "use_cache": kwargs.get("use_cache"),
                "position_ids": position_ids,
                "cache_position": cache_position,
            }
        )
        return model_inputs

    @staticmethod
    def _reorder_cache(past_key_values, beam_idx):
        reordered_past = ()
        for layer_past in past_key_values:
            reordered_past += (
                tuple(
                    past_state.index_select(0, beam_idx.to(past_state.device))
                    for past_state in layer_past
                ),
            )
        return reordered_past


HelpingAIConfig.register_for_auto_class()
HelpingAIForCausalLM.register_for_auto_class("AutoModelForCausalLM")
