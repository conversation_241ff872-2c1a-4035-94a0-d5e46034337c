from transformers.configuration_utils import PretrainedConfig
from transformers.utils import logging


logger = logging.get_logger(__name__)


class HelpingAIConfig(PretrainedConfig):
    model_type = "HelpingAI"
    keys_to_ignore_at_inference = ["past_key_values"]

    def __init__(
        self,
        vocab_size=50281,
        hidden_size=2560,
        num_hidden_layers=32,
        num_attention_heads=32,
        num_key_value_heads=None,  # New: Support for GQA
        head_dim=None,  # Will be calculated if not provided
        num_local_experts=8,
        num_experts_per_tok=2,
        intermediate_size=6912,
        hidden_act="silu",
        hidden_dropout=0.0,
        attention_dropout=0.0,  # Enhanced: Now properly used
        classifier_dropout=0.1,
        max_position_embeddings=65536,  # Enhanced: Support up to 64k
        initializer_range=0.02,
        rms_norm_eps=1e-6,  # Enhanced: Using RMS norm consistently
        use_cache=True,  # Enhanced: Default to True for better performance
        bos_token_id=50278,
        eos_token_id=50279,
        pad_token_id=50279,
        tie_word_embeddings=False,
        rope_theta=10000.0,  # Enhanced: Better default
        rope_scaling=None,  # New: Advanced RoPE scaling support
        attention_bias=False,  # New: Control attention biases
        use_sliding_window=False,  # New: Sliding window attention
        sliding_window=4096,  # New: Sliding window size
        max_window_layers=28,  # New: Layers using full attention
        layer_types=None,  # New: Custom attention patterns per layer
        # Legacy parameters for backward compatibility
        rope_pct=0.25,
        partial_rotary_factor=0.25,
        use_qkv_bias=None,  # Will map to attention_bias
        output_router_logits=False,
        router_aux_loss_coef=0.02,
        **kwargs,
    ):
        super().__init__(bos_token_id=bos_token_id, eos_token_id=eos_token_id, **kwargs)

        # Basic model parameters
        self.vocab_size = vocab_size
        self.max_position_embeddings = max_position_embeddings
        self.hidden_size = hidden_size
        self.num_hidden_layers = num_hidden_layers
        self.num_attention_heads = num_attention_heads

        # Enhanced: Support for GQA (Grouped Query Attention)
        if num_key_value_heads is None:
            self.num_key_value_heads = num_attention_heads
        else:
            self.num_key_value_heads = num_key_value_heads

        # Enhanced: Calculate head_dim if not provided
        if head_dim is None:
            self.head_dim = self.hidden_size // self.num_attention_heads
        else:
            self.head_dim = head_dim

        # MoE parameters
        self.num_local_experts = num_local_experts
        self.num_experts_per_tok = num_experts_per_tok
        self.intermediate_size = intermediate_size

        # Activation and dropout
        self.hidden_act = hidden_act
        self.hidden_dropout = hidden_dropout
        self.attention_dropout = attention_dropout
        self.classifier_dropout = classifier_dropout

        # Initialization and normalization
        self.initializer_range = initializer_range
        self.rms_norm_eps = rms_norm_eps
        self.use_cache = use_cache
        self.tie_word_embeddings = tie_word_embeddings

        # Enhanced: RoPE configuration
        self.rope_theta = rope_theta
        self.rope_scaling = rope_scaling

        # Enhanced: Attention configuration
        # Handle backward compatibility for use_qkv_bias
        if use_qkv_bias is not None:
            self.attention_bias = use_qkv_bias
        else:
            self.attention_bias = attention_bias

        # Enhanced: Sliding window attention
        self.use_sliding_window = use_sliding_window
        self.sliding_window = sliding_window
        self.max_window_layers = max_window_layers
        self.layer_types = layer_types

        # Legacy parameters for backward compatibility
        self.rope_pct = rope_pct
        self.partial_rotary_factor = partial_rotary_factor
        self.output_router_logits = output_router_logits
        self.router_aux_loss_coef = router_aux_loss_coef

        # Enhanced validation
        if self.hidden_size % self.num_attention_heads != 0:
            raise ValueError(
                f"The hidden size ({self.hidden_size}) is not divisible by the number of attention heads ({self.num_attention_heads})! "
                "Make sure to update them!"
            )

        if self.num_attention_heads % self.num_key_value_heads != 0:
            raise ValueError(
                f"The number of attention heads ({self.num_attention_heads}) must be divisible by the number of "
                f"key-value heads ({self.num_key_value_heads})."
            )

        # Validate rope scaling configuration
        self._rope_scaling_validation()

    def _rope_scaling_validation(self):
        """
        Enhanced validation for the `rope_scaling` configuration supporting multiple RoPE types.
        """
        if self.rope_scaling is None:
            return

        if not isinstance(self.rope_scaling, dict):
            raise ValueError(f"`rope_scaling` must be a dictionary, got {self.rope_scaling}")

        rope_scaling_type = self.rope_scaling.get("type", None)
        rope_scaling_factor = self.rope_scaling.get("factor", None)

        # Supported RoPE scaling types
        valid_types = ["default", "linear", "dynamic", "yarn", "longrope", "llama3"]

        if rope_scaling_type is None or rope_scaling_type not in valid_types:
            raise ValueError(
                f"`rope_scaling`'s type field must be one of {valid_types}, got {rope_scaling_type}"
            )

        # Validate factor for non-default types
        if rope_scaling_type != "default":
            if rope_scaling_factor is None or not isinstance(rope_scaling_factor, (int, float)) or rope_scaling_factor <= 1.0:
                raise ValueError(f"`rope_scaling`'s factor field must be a number > 1, got {rope_scaling_factor}")

        # Type-specific validations
        if rope_scaling_type in ["dynamic", "longrope", "llama3"]:
            original_max_pos = self.rope_scaling.get("original_max_position_embeddings", None)
            if original_max_pos is not None and not isinstance(original_max_pos, int):
                raise ValueError(f"`rope_scaling`'s original_max_position_embeddings field must be an int, got {original_max_pos}")

        if rope_scaling_type == "yarn":
            attention_factor = self.rope_scaling.get("attention_factor", None)
            if attention_factor is not None and not isinstance(attention_factor, (int, float)):
                raise ValueError(f"`rope_scaling`'s attention_factor field must be a number, got {attention_factor}")

        if rope_scaling_type == "longrope":
            short_factor = self.rope_scaling.get("short_factor", None)
            long_factor = self.rope_scaling.get("long_factor", None)
            if short_factor is not None and not isinstance(short_factor, list):
                raise ValueError(f"`rope_scaling`'s short_factor field must be a list, got {short_factor}")
            if long_factor is not None and not isinstance(long_factor, list):
                raise ValueError(f"`rope_scaling`'s long_factor field must be a list, got {long_factor}")

        if rope_scaling_type == "llama3":
            low_freq_factor = self.rope_scaling.get("low_freq_factor", None)
            high_freq_factor = self.rope_scaling.get("high_freq_factor", None)
            if low_freq_factor is not None and not isinstance(low_freq_factor, (int, float)):
                raise ValueError(f"`rope_scaling`'s low_freq_factor field must be a number, got {low_freq_factor}")
            if high_freq_factor is not None and not isinstance(high_freq_factor, (int, float)):
                raise ValueError(f"`rope_scaling`'s high_freq_factor field must be a number, got {high_freq_factor}")