#!/usr/bin/env python3
"""
Specific test for 64k context length support in the enhanced HelpingAI model.
This test validates that the model can handle very long sequences up to 64k tokens.
"""

import torch
import torch.nn as nn
import sys
import os
import time
import gc

# Add the HelpingAI directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'HelpingAI_'))

from configuration_HelpingAI import HelpingAIConfig
from modeling_HelpingAI import HelpingAIForCausalLM


def test_64k_context_configuration():
    """Test configuration for 64k context length."""
    print("Testing 64k context configuration...")
    
    config = HelpingAIConfig(
        vocab_size=32000,
        hidden_size=1024,
        num_hidden_layers=8,
        num_attention_heads=16,
        num_key_value_heads=8,  # GQA for efficiency
        max_position_embeddings=65536,  # 64k context
        intermediate_size=2048,
        use_sliding_window=True,
        sliding_window=4096,
        max_window_layers=4,  # First 4 layers use full attention
        rope_scaling={
            "type": "linear",
            "factor": 4.0  # Scale for 64k context
        }
    )
    
    print(f"✓ 64k context configuration created")
    print(f"  - Max position embeddings: {config.max_position_embeddings:,}")
    print(f"  - RoPE scaling: {config.rope_scaling}")
    print(f"  - Sliding window: {config.sliding_window}")
    print(f"  - GQA ratio: {config.num_attention_heads // config.num_key_value_heads}")
    
    return config


def test_progressive_context_lengths():
    """Test the model with progressively longer context lengths."""
    print("\nTesting progressive context lengths...")
    
    config = test_64k_context_configuration()
    model = HelpingAIForCausalLM(config)
    model.eval()
    
    # Test different context lengths
    test_lengths = [1024, 4096, 8192, 16384, 32768]
    
    # Add 65536 only if we have enough memory
    if torch.cuda.is_available():
        device = torch.device("cuda")
        model = model.to(device)
        # Only test 64k on GPU due to memory requirements
        test_lengths.append(65536)
    else:
        device = torch.device("cpu")
        print("  Note: Testing on CPU, skipping 64k test due to memory constraints")
    
    batch_size = 1  # Use batch size 1 for long sequences
    
    for seq_len in test_lengths:
        print(f"\n  Testing sequence length: {seq_len:,}")
        
        try:
            # Generate random input
            input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_len), device=device)
            
            # Measure memory and time
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.reset_peak_memory_stats()
                start_memory = torch.cuda.memory_allocated()
            
            start_time = time.time()
            
            with torch.no_grad():
                outputs = model(input_ids, use_cache=False)  # Disable cache to save memory
            
            end_time = time.time()
            
            if torch.cuda.is_available():
                end_memory = torch.cuda.memory_allocated()
                peak_memory = torch.cuda.max_memory_allocated()
                memory_used = (peak_memory - start_memory) / 1024**3  # GB
            else:
                memory_used = 0
            
            time_taken = end_time - start_time
            
            print(f"    ✓ Success! Output shape: {outputs.logits.shape}")
            print(f"    ⏱️  Time: {time_taken:.2f}s")
            if torch.cuda.is_available():
                print(f"    💾 Memory used: {memory_used:.2f} GB")
            
            # Clean up
            del outputs, input_ids
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()
            
        except Exception as e:
            print(f"    ❌ Failed: {str(e)}")
            # Clean up on failure
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()
            
            # If we fail at a certain length, don't test longer ones
            if seq_len < 32768:
                print(f"    Stopping tests due to failure at {seq_len:,} tokens")
                break
    
    return True


def test_rope_scaling_effectiveness():
    """Test that RoPE scaling actually works for long sequences."""
    print("\nTesting RoPE scaling effectiveness...")
    
    # Test without scaling
    config_no_scaling = HelpingAIConfig(
        vocab_size=1000,
        hidden_size=256,
        num_hidden_layers=2,
        num_attention_heads=4,
        max_position_embeddings=4096,
        rope_scaling=None
    )
    
    # Test with scaling
    config_with_scaling = HelpingAIConfig(
        vocab_size=1000,
        hidden_size=256,
        num_hidden_layers=2,
        num_attention_heads=4,
        max_position_embeddings=16384,  # 4x longer
        rope_scaling={
            "type": "linear",
            "factor": 4.0
        }
    )
    
    model_no_scaling = HelpingAIForCausalLM(config_no_scaling)
    model_with_scaling = HelpingAIForCausalLM(config_with_scaling)
    
    # Test sequence that would exceed original context
    seq_len = 8192
    input_ids = torch.randint(0, 1000, (1, seq_len))
    
    print(f"  Testing sequence length: {seq_len}")
    
    # Test model without scaling (should work but may have degraded performance)
    try:
        with torch.no_grad():
            outputs_no_scaling = model_no_scaling(input_ids)
        print("  ✓ Model without RoPE scaling handled long sequence")
    except Exception as e:
        print(f"  ⚠️  Model without RoPE scaling failed: {str(e)}")
    
    # Test model with scaling (should work well)
    try:
        with torch.no_grad():
            outputs_with_scaling = model_with_scaling(input_ids)
        print("  ✓ Model with RoPE scaling handled long sequence")
    except Exception as e:
        print(f"  ❌ Model with RoPE scaling failed: {str(e)}")
        return False
    
    return True


def test_sliding_window_memory_efficiency():
    """Test that sliding window attention reduces memory usage."""
    print("\nTesting sliding window memory efficiency...")
    
    if not torch.cuda.is_available():
        print("  Skipping memory efficiency test (requires CUDA)")
        return True
    
    device = torch.device("cuda")
    
    # Configuration without sliding window
    config_full = HelpingAIConfig(
        vocab_size=1000,
        hidden_size=512,
        num_hidden_layers=4,
        num_attention_heads=8,
        use_sliding_window=False,
    )
    
    # Configuration with sliding window
    config_sliding = HelpingAIConfig(
        vocab_size=1000,
        hidden_size=512,
        num_hidden_layers=4,
        num_attention_heads=8,
        use_sliding_window=True,
        sliding_window=1024,
        max_window_layers=2,
    )
    
    seq_len = 4096
    input_ids = torch.randint(0, 1000, (1, seq_len), device=device)
    
    # Test full attention
    model_full = HelpingAIForCausalLM(config_full).to(device)
    torch.cuda.empty_cache()
    torch.cuda.reset_peak_memory_stats()
    
    with torch.no_grad():
        outputs_full = model_full(input_ids)
    
    memory_full = torch.cuda.max_memory_allocated() / 1024**2  # MB
    
    # Test sliding window attention
    model_sliding = HelpingAIForCausalLM(config_sliding).to(device)
    torch.cuda.empty_cache()
    torch.cuda.reset_peak_memory_stats()
    
    with torch.no_grad():
        outputs_sliding = model_sliding(input_ids)
    
    memory_sliding = torch.cuda.max_memory_allocated() / 1024**2  # MB
    
    memory_reduction = (memory_full - memory_sliding) / memory_full * 100
    
    print(f"  Full attention memory: {memory_full:.1f} MB")
    print(f"  Sliding window memory: {memory_sliding:.1f} MB")
    print(f"  Memory reduction: {memory_reduction:.1f}%")
    
    if memory_sliding < memory_full:
        print("  ✓ Sliding window attention reduces memory usage")
    else:
        print("  ⚠️  Sliding window attention did not reduce memory (may be due to small sequence)")
    
    return True


def run_64k_tests():
    """Run all 64k context tests."""
    print("=" * 70)
    print("HelpingAI 64K Context Length Test Suite")
    print("=" * 70)
    
    try:
        test_progressive_context_lengths()
        test_rope_scaling_effectiveness()
        test_sliding_window_memory_efficiency()
        
        print("\n" + "=" * 70)
        print("🎉 64K CONTEXT TESTS COMPLETED! 🎉")
        print("The enhanced HelpingAI model supports extended context lengths.")
        print("Key features validated:")
        print("  ✓ Progressive context length scaling")
        print("  ✓ RoPE scaling for long sequences")
        print("  ✓ Sliding window attention efficiency")
        print("  ✓ Memory optimization for long contexts")
        print("=" * 70)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 64K TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_64k_tests()
    sys.exit(0 if success else 1)
