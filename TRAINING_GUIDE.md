# HelpingAI Enhanced Architecture Training Guide

This guide explains how to fine-tune any model to use the enhanced HelpingAI architecture with 64k context support, advanced RoPE scaling, sliding window attention, and other improvements.

## 🎯 Recommended Training Data Sizes

### By Model Size:
- **Small models (1-3B parameters)**: 10K-50K high-quality samples
- **Medium models (7-8B parameters)**: 50K-200K samples  
- **Large models (13B+ parameters)**: 100K-500K samples

### By Task Type:
- **General conversation**: Base recommendations above
- **Reasoning/thinking models**: Add 20-30% more data focused on step-by-step reasoning
- **Domain-specific**: 5K-20K domain samples + 10K-50K general samples
- **Code generation**: 20K-100K code samples + 10K-50K general samples

### Quality vs Quantity:
- **High-quality curated data**: Use lower end of ranges
- **Mixed quality data**: Use higher end of ranges
- **Synthetic data**: Can use 2-3x more samples

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install torch transformers datasets peft accelerate wandb
pip install bitsandbytes  # For 8-bit optimization
```

### 2. Basic Training
```bash
python train.py --config training_configs/base_config.json
```

### 3. Small Model Training (Fast)
```bash
python train.py --config training_configs/small_model_config.json
```

### 4. Large Model with 64K Context
```bash
python train.py --config training_configs/large_64k_config.json
```

## 📊 Configuration Options

### Model Architecture
- `hidden_size`: Hidden dimension (768, 1024, 2560, 4096)
- `num_layers`: Number of transformer layers (12, 24, 32, 48)
- `num_heads`: Number of attention heads (12, 16, 32, 64)
- `num_kv_heads`: Number of key-value heads for GQA (4, 8, 16)
- `max_seq_length`: Maximum sequence length (4096, 8192, 32768, 65536)

### Enhanced Features
- `use_sliding_window`: Enable sliding window attention for long contexts
- `sliding_window`: Window size for sliding attention (2048, 4096, 8192)
- `rope_scaling`: RoPE scaling configuration for extended context
  - `type`: "linear", "dynamic", "yarn", "longrope", "llama3"
  - `factor`: Scaling factor (1.5, 2.0, 4.0, 8.0, 16.0)

### LoRA Configuration
- `lora_r`: LoRA rank (8, 16, 32, 64, 128)
- `lora_alpha`: LoRA alpha (usually same as rank)
- `lora_dropout`: LoRA dropout (0.05, 0.1)
- `use_rslora`: Use rank-stabilized LoRA for better stability

### Training Parameters
- `num_epochs`: Training epochs (1-5 for fine-tuning)
- `batch_size`: Per-device batch size (1-8 depending on memory)
- `gradient_accumulation_steps`: Accumulation steps (4-32)
- `learning_rate`: Learning rate (1e-5 to 5e-4)
- `warmup_steps`: Warmup steps (50-500)

## 💾 Memory Requirements

### GPU Memory Usage (with LoRA):
- **Small model (1B)**: 6-8 GB VRAM
- **Medium model (7B)**: 16-24 GB VRAM  
- **Large model (13B)**: 24-40 GB VRAM
- **64K context**: Add 50-100% more memory

### Memory Optimization Tips:
1. Use `gradient_checkpointing: true`
2. Use `fp16: true` or `bf16: true`
3. Reduce `batch_size` and increase `gradient_accumulation_steps`
4. Use `optimizer: "adamw_8bit"`
5. Enable sliding window attention for long contexts

## 📈 Training Recommendations

### Learning Rate Schedule:
- **Small models**: 3e-4 to 5e-4
- **Medium models**: 1e-4 to 3e-4
- **Large models**: 5e-5 to 1e-4
- **64K context**: Reduce by 50%

### Epochs:
- **General fine-tuning**: 2-3 epochs
- **Domain adaptation**: 3-5 epochs
- **Reasoning training**: 1-2 epochs (high-quality data)

### Batch Size Strategy:
- Start with `batch_size=1` and `gradient_accumulation_steps=16`
- Increase batch size if memory allows
- Effective batch size = batch_size × gradient_accumulation_steps × num_gpus

## 🎛️ Advanced Features

### RoPE Scaling for Long Context:
```json
{
  "rope_scaling": {
    "type": "linear",
    "factor": 4.0
  }
}
```

### Sliding Window Attention:
```json
{
  "use_sliding_window": true,
  "sliding_window": 4096,
  "max_window_layers": 16
}
```

### Grouped Query Attention (GQA):
```json
{
  "num_heads": 32,
  "num_kv_heads": 8
}
```

## 🔧 Troubleshooting

### Out of Memory:
1. Reduce `batch_size` to 1
2. Increase `gradient_accumulation_steps`
3. Enable `gradient_checkpointing`
4. Use `optimizer: "adamw_8bit"`
5. Reduce `max_seq_length`

### Slow Training:
1. Increase `batch_size` if memory allows
2. Use multiple GPUs with `accelerate`
3. Enable `fp16` or `bf16`
4. Use `dataloader_pin_memory: false`

### Poor Convergence:
1. Reduce learning rate
2. Increase warmup steps
3. Use `lr_scheduler: "cosine"`
4. Check data quality and formatting

## 📝 Data Format

### Conversation Format:
```json
[
  {"role": "user", "content": "Hello!"},
  {"role": "assistant", "content": "Hi there!"}
]
```

### Instruction Format:
```json
{
  "instruction": "Explain quantum computing",
  "output": "Quantum computing is..."
}
```

### Text Format:
```json
{
  "text": "Complete conversation or text..."
}
```

## 🎯 Best Practices

1. **Start small**: Begin with a small model and short context
2. **Quality over quantity**: Better to have fewer high-quality samples
3. **Monitor training**: Watch loss curves and validation metrics
4. **Save checkpoints**: Regular saves prevent data loss
5. **Test incrementally**: Validate improvements at each step

## 📊 Expected Results

### Training Time (approximate):
- **Small model (1B)**: 2-6 hours on single GPU
- **Medium model (7B)**: 8-24 hours on single GPU
- **Large model (13B)**: 1-3 days on single GPU
- **64K context**: 2-5x longer training time

### Performance Improvements:
- **Context length**: Up to 64K tokens (16x improvement)
- **Memory efficiency**: 30-50% reduction with sliding window
- **Training speed**: 20-40% faster with GQA
- **Quality**: Better long-range dependencies with enhanced RoPE

## 🚀 Production Deployment

After training, convert to production format:
```python
# Merge LoRA weights
from peft import PeftModel
base_model = HelpingAIForCausalLM.from_pretrained("base_model")
model = PeftModel.from_pretrained(base_model, "path/to/lora")
merged_model = model.merge_and_unload()
merged_model.save_pretrained("final_model")
```

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review configuration examples
3. Test with smaller models first
4. Monitor GPU memory usage
